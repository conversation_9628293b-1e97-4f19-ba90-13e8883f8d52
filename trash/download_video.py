
import argparse
import os
import yt_dlp

def download_video(video_identifier, output_path, cookies_file=None):
    """
    Downloads a video from a given URL or video ID using yt-dlp.
    """
    if not os.path.exists(output_path):
        os.makedirs(output_path)

    ydl_opts = {
        'format': 'bestvideo[ext=mp4]+bestaudio[ext=m4a]/best[ext=mp4]/best',
        'outtmpl': os.path.join(output_path, '%(title)s.%(ext)s'),
    }
    if cookies_file:
        ydl_opts['cookiefile'] = cookies_file

    try:
        with yt_dlp.YoutubeDL(ydl_opts) as ydl:
            ydl.download([video_identifier])
        print(f"Video downloaded successfully to {output_path}")
    except Exception as e:
        print(f"An error occurred: {e}")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Download a video from a URL or video ID.")
    parser.add_argument("video_identifier", help="The full URL or video ID of the video to download.")
    parser.add_argument("--cookies", help="Path to the cookies file.", default=None)
    args = parser.parse_args()

    output_directory = "videos/"
    download_video(args.video_identifier, output_directory, args.cookies)
