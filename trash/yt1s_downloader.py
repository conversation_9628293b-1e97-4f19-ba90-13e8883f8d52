import requests
import argparse
import os

BASE_URL = "https://v1.yt1s.biz"

def download_video_from_yt1s(youtube_url, output_path):
    """
    Downloads a video from YouTube using the yt1s.biz service.
    """
    if not os.path.exists(output_path):
        os.makedirs(output_path)

    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.36'
    }

    # First request to get video info
    try:
        search_url = f"{BASE_URL}/api/ajaxSearch"
        payload = {"q": youtube_url, "vt": "home"}
        response = requests.post(search_url, data=payload, headers=headers)
        response.raise_for_status()
        video_info = response.json()

        if video_info.get("status") != "ok":
            print(f"Error getting video info: {video_info.get('mess')}")
            return

        vid = video_info.get("vid")
        key = ""
        for format in video_info.get("links", {}).get("mp4", {}):
            if video_info["links"]["mp4"][format]["q"] == "1080p":
                key = video_info["links"]["mp4"][format]["k"]
                break
        if not key:
             for format in video_info.get("links", {}).get("mp4", {}):
                if video_info["links"]["mp4"][format]["q"] == "720p":
                    key = video_info["links"]["mp4"][format]["k"]
                    break
        if not key:
            print("Could not find a 1080p or 720p mp4 video.")
            return

        # Second request to get download link
        convert_url = f"{BASE_URL}/api/ajaxConvert"
        payload = {"vid": vid, "k": key}
        response = requests.post(convert_url, data=payload, headers=headers)
        response.raise_for_status()
        convert_info = response.json()

        if convert_info.get("status") != "ok":
            print(f"Error getting download link: {convert_info.get('mess')}")
            return

        download_link = convert_info.get("dlink")
        if not download_link:
            print("Could not find download link.")
            return

        # Download the video
        video_title = video_info.get("title", "video")
        file_path = os.path.join(output_path, f"{video_title}.mp4")

        print(f"Downloading {video_title}...")
        with requests.get(download_link, stream=True) as r:
            r.raise_for_status()
            with open(file_path, 'wb') as f:
                for chunk in r.iter_content(chunk_size=8192):
                    f.write(chunk)
        print(f"Video downloaded successfully to {file_path}")

    except requests.exceptions.RequestException as e:
        print(f"An error occurred: {e}")
    except Exception as e:
        print(f"An error occurred: {e}")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Download a YouTube video using yt1s.biz.")
    parser.add_argument("youtube_url", help="The full URL of the YouTube video to download.")
    args = parser.parse_args()

    output_directory = "videos/"
    download_video_from_yt1s(args.youtube_url, output_directory)