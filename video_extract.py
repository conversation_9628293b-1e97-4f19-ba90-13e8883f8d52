#!/usr/bin/env python3
"""
Video Processing Script with GIF Overlay

Features:
- Download videos from URLs using yt-dlp
- Extract a clip from the middle of a video.
- Convert to a specified aspect ratio.
- Add animated GIF and logo overlays.
- Add a title to the video.
- Comprehensive command-line interface for customization.
- Return results as JSON
"""

import ffmpeg
import sys
import os
import argparse
import json
import requests
from pathlib import Path
from typing import Optional, Dict, Any
from urllib.parse import urlparse


def download_video(video_url: str, video_title: str, output_dir: str = "videos") -> Dict[str, Any]:
    """
    Download a video from a direct URL using HTTP requests.

    Args:
        video_url: Direct URL of the video file to download
        video_title: Custom title for the video file
        output_dir: Directory to save the video (default: "videos")

    Returns:
        Dict containing success status, file path, and any error messages
    """
    result = {
        "success": False,
        "file_path": None,
        "error": None,
        "title": video_title,
        "url": video_url
    }

    try:
        # Create output directory if it doesn't exist
        os.makedirs(output_dir, exist_ok=True)

        # Sanitize the title for filename
        safe_title = "".join(c for c in video_title if c.isalnum() or c in (' ', '-', '_')).rstrip()
        if not safe_title:
            safe_title = "downloaded_video"

        # Get file extension from URL or default to .mp4
        parsed_url = urlparse(video_url)
        url_path = parsed_url.path
        if '.' in url_path:
            extension = os.path.splitext(url_path)[1]
        else:
            extension = '.mp4'

        # Construct output file path
        output_file = os.path.join(output_dir, f"{safe_title}{extension}")

        print(f"Downloading from: {video_url}")
        print(f"Saving to: {output_file}")

        # Download the video file
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }

        response = requests.get(video_url, headers=headers, stream=True)
        response.raise_for_status()

        # Check if the response contains video content
        content_type = response.headers.get('content-type', '').lower()
        if not any(video_type in content_type for video_type in ['video/', 'application/octet-stream']):
            result["error"] = f"URL does not appear to contain video content. Content-Type: {content_type}"
            return result

        # Write the file
        total_size = int(response.headers.get('content-length', 0))
        downloaded_size = 0

        with open(output_file, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    f.write(chunk)
                    downloaded_size += len(chunk)
                    if total_size > 0:
                        progress = (downloaded_size / total_size) * 100
                        print(f"\rDownload progress: {progress:.1f}%", end='', flush=True)

        print()  # New line after progress

        if os.path.exists(output_file) and os.path.getsize(output_file) > 0:
            result["success"] = True
            result["file_path"] = output_file
            result["file_size"] = os.path.getsize(output_file)
            print(f"Successfully downloaded: {output_file} ({result['file_size']} bytes)")
        else:
            result["error"] = "Downloaded file is empty or not found"

    except requests.exceptions.RequestException as e:
        result["error"] = f"Network error: {str(e)}"
    except Exception as e:
        result["error"] = f"Download error: {str(e)}"

    return result


def get_video_duration(input_file: str) -> Optional[float]:
    """Get the duration of a video file in seconds."""
    try:
        probe = ffmpeg.probe(input_file)
        duration = float(probe['streams'][0]['duration'])
        return duration
    except Exception as e:
        print(f"Error getting video duration: {e}")
        return None


def get_logo_path(logo_name: str) -> str:
    """
    Get the logo file path based on the logo name.

    Args:
        logo_name: Either "menuhub" or "lexitone"

    Returns:
        Path to the logo file

    Raises:
        ValueError: If logo_name is not valid or file doesn't exist
    """
    valid_logos = ["menuhub", "lexitone"]

    if logo_name.lower() not in valid_logos:
        raise ValueError(f"Logo must be one of {valid_logos}, got: {logo_name}")

    logo_filename = f"logo-{logo_name.lower()}.png"

    if not os.path.exists(logo_filename):
        raise ValueError(f"Logo file not found: {logo_filename}")

    return logo_filename


def add_title_overlay(video_stream, title: str):
    """Adds a title overlay to the video stream, positioned at the bottom."""
    return video_stream.drawtext(
        text=title,
        x='(w-text_w)/2',
        y='h-th-40',  # Positioned at the bottom
        fontsize=48,
        fontcolor='white',
        shadowcolor='black',
        shadowx=2,
        shadowy=2,
    )


def extract_and_convert_video(
    input_file: str,
    output_file: Optional[str] = None,
    gif_overlay_path: Optional[str] = None,
    logo_name: Optional[str] = None,
    video_title: Optional[str] = None,
    duration_seconds: int = 30,
    aspect_ratio: str = "9:16",
) -> Dict[str, Any]:
    """
    Extract a clip from the middle of a video, convert to a specific aspect ratio, and add overlays.

    Args:
        input_file: Path to input video file.
        output_file: Path to output video file (optional).
        gif_overlay_path: Path to GIF file for overlay (optional).
        logo_name: Logo name ("menuhub" or "lexitone").
        video_title: Text to display as a title on the video.
        duration_seconds: Duration of the extracted clip in seconds.
        aspect_ratio: Target aspect ratio (e.g., "9:16").

    Returns:
        Dict containing processing results and metadata
    """

    # Validate logo if provided
    logo_overlay_path = None
    if logo_name:
        try:
            logo_overlay_path = get_logo_path(logo_name)
        except ValueError as e:
            return {
                "success": False,
                "input_file": input_file,
                "output_file": None,
                "duration_seconds": duration_seconds,
                "aspect_ratio": aspect_ratio,
                "logo_name": logo_name,
                "video_title": video_title,
                "error": str(e),
                "video_duration": None,
                "start_time": None,
                "target_dimensions": None,
                "overlays_applied": {
                    "gif": False,
                    "logo": False,
                    "title": False
                }
            }

    result = {
        "success": False,
        "input_file": input_file,
        "output_file": None,
        "duration_seconds": duration_seconds,
        "aspect_ratio": aspect_ratio,
        "logo_name": logo_name,
        "video_title": video_title,
        "error": None,
        "video_duration": None,
        "start_time": None,
        "target_dimensions": None,
        "overlays_applied": {
            "gif": False,
            "logo": False,
            "title": False
        }
    }

    if not os.path.exists(input_file):
        result["error"] = f"Input file '{input_file}' not found."
        return result
    
    # Get video duration
    video_duration = get_video_duration(input_file)
    if video_duration is None:
        result["error"] = "Could not determine video duration"
        return result

    result["video_duration"] = video_duration
    print(f"Video duration: {video_duration:.2f} seconds")

    # Check if video is long enough
    if video_duration < duration_seconds:
        result["error"] = f"Video is only {video_duration:.2f} seconds long, need at least {duration_seconds} seconds."
        return result

    # Calculate start time for the middle clip
    start_time = (video_duration - duration_seconds) / 2
    result["start_time"] = start_time
    print(f"Extracting {duration_seconds} seconds starting from {start_time:.2f} seconds")

    # Set output filename if not provided
    if output_file is None:
        input_path = Path(input_file)
        output_file = input_path.with_name(f"{input_path.stem}_extract{input_path.suffix}")

    result["output_file"] = str(output_file)
    
    try:
        # Get original video dimensions
        probe = ffmpeg.probe(input_file)
        video_stream_info = next(s for s in probe['streams'] if s['codec_type'] == 'video')
        original_width = int(video_stream_info['width'])
        original_height = int(video_stream_info['height'])
        
        print(f"Original dimensions: {original_width}x{original_height}")
        
        # Calculate target dimensions for the given aspect ratio
        try:
            ratio_width, ratio_height = map(int, aspect_ratio.split(':'))
        except ValueError:
            result["error"] = f"Invalid aspect ratio format '{aspect_ratio}'. Please use 'width:height' format (e.g., '9:16')."
            return result

        target_height = original_height
        target_width = int(target_height * ratio_width / ratio_height)

        if target_width > original_width:
            target_width = original_width
            target_height = int(target_width * ratio_height / ratio_width)

        result["target_dimensions"] = {"width": target_width, "height": target_height}
        print(f"Target dimensions for {aspect_ratio} aspect ratio: {target_width}x{target_height}")
        
        # Create ffmpeg stream
        input_stream = ffmpeg.input(input_file, ss=start_time, t=duration_seconds)
        
        # Apply video filters: scale and crop
        video = input_stream.video.filter('scale', target_width, target_height, force_original_aspect_ratio='increase')
        video = video.filter('crop', target_width, target_height)
        
        # Add GIF overlay if provided (centered)
        if gif_overlay_path and os.path.exists(gif_overlay_path):
            print(f"Adding GIF overlay from: {gif_overlay_path}")
            gif_stream = ffmpeg.input(gif_overlay_path, stream_loop=-1)
            gif_stream = gif_stream.filter('scale', 'iw*2', 'ih*2')
            video = ffmpeg.overlay(video, gif_stream, x='(W-w)/2', y='(H-h)/2', eof_action='pass')
            result["overlays_applied"]["gif"] = True
        elif gif_overlay_path:
            print(f"Warning: GIF overlay file not found at '{gif_overlay_path}'. Skipping overlay.")

        # Add logo overlay if provided (top center)
        if logo_overlay_path and os.path.exists(logo_overlay_path):
            print(f"Adding logo overlay from: {logo_overlay_path}")
            logo_stream = ffmpeg.input(logo_overlay_path)
            logo_stream = logo_stream.filter('scale', 'iw*0.9', 'ih*0.9')
            video = ffmpeg.overlay(video, logo_stream, x='(W-w)/2', y='20')
            result["overlays_applied"]["logo"] = True
        elif logo_overlay_path:
            print(f"Warning: Logo overlay file not found at '{logo_overlay_path}'. Skipping overlay.")

        # Add title overlay
        if video_title:
            video = add_title_overlay(video, video_title)
            result["overlays_applied"]["title"] = True

        # Get audio stream
        audio = input_stream.audio
        
        # Output with video and audio
        output = ffmpeg.output(video, audio, str(output_file),
                             vcodec='libx264', 
                             acodec='aac',
                             preset='fast',
                             crf=23)
        
        # Run the conversion
        print(f"Processing video... Output will be saved as: {output_file}")
        ffmpeg.run(output, overwrite_output=True, quiet=False)

        print(f"Successfully created: {output_file}")
        result["success"] = True
        return result

    except Exception as e:
        print(f"Error processing video: {e}")
        result["error"] = str(e)
        return result


def main():
    """Main function to handle command line arguments and run the conversion."""

    parser = argparse.ArgumentParser(
        description="Download and process videos with overlays. Returns JSON results.",
        formatter_class=argparse.RawTextHelpFormatter
    )

    # New primary parameters
    parser.add_argument("--video-url",
                        help="URL of the video to download")
    parser.add_argument("--video-title",
                        help="Title for the downloaded video file")

    # Legacy support for local files
    parser.add_argument("input_file", nargs='?', default=None,
                        help="Path to the input video file (optional if using --video-url)")
    parser.add_argument("output_file", nargs='?', default=None,
                        help="Path to the output video file (optional, defaults to [input_name]_extract.mp4)")

    # Processing options
    parser.add_argument("--gif", default="animated-circle.gif",
                        help="Path to the GIF file to overlay (default: animated-circle.gif)")
    parser.add_argument("--logo", required=True, choices=["menuhub", "lexitone"],
                        help="Logo to use: 'menuhub' or 'lexitone' (required)")
    parser.add_argument("--title", required=True,
                        help="Title text to display on the video (required)")
    parser.add_argument("--duration", type=int, default=30,
                        help="Duration of the extracted clip in seconds (default: 30)")
    parser.add_argument("--aspect_ratio", default="9:16",
                        help="Target aspect ratio, e.g., '9:16' (default: 9:16)")

    args = parser.parse_args()

    final_result = {
        "download": None,
        "processing": None,
        "success": False,
        "error": None
    }

    input_file = args.input_file

    # Handle video download if URL is provided
    if args.video_url:
        if not args.video_title:
            final_result["error"] = "Both --video-url and --video-title are required when downloading"
            print(json.dumps(final_result, indent=2))
            sys.exit(1)

        print(f"Downloading video from: {args.video_url}")
        download_result = download_video(args.video_url, args.video_title)
        final_result["download"] = download_result

        if not download_result["success"]:
            final_result["error"] = f"Download failed: {download_result['error']}"
            print(json.dumps(final_result, indent=2))
            sys.exit(1)

        input_file = download_result["file_path"]
        print(f"Downloaded to: {input_file}")

    elif not input_file:
        final_result["error"] = "Either provide --video-url and --video-title, or specify an input_file"
        print(json.dumps(final_result, indent=2))
        sys.exit(1)

    # Process the video
    print(f"Processing: {input_file}")

    processing_result = extract_and_convert_video(
        input_file,
        args.output_file,
        gif_overlay_path=args.gif,
        logo_overlay_path=args.logo,
        duration_seconds=args.duration,
        aspect_ratio=args.aspect_ratio,
        title=args.title,
    )

    final_result["processing"] = processing_result
    final_result["success"] = processing_result["success"]

    if not processing_result["success"]:
        final_result["error"] = f"Processing failed: {processing_result['error']}"

    # Output JSON result
    print(json.dumps(final_result, indent=2))

    if not final_result["success"]:
        sys.exit(1)


if __name__ == "__main__":
    main()
