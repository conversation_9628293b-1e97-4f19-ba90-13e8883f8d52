#!/usr/bin/env python3
"""
Video Processing Script with GIF Overlay

Features:
- Extract a clip from the middle of a video.
- Convert to a specified aspect ratio.
- Add animated GIF and logo overlays.
- Add a title to the video.
- Comprehensive command-line interface for customization.
"""

import ffmpeg
import sys
import os
import argparse
from pathlib import Path
from typing import Optional


def get_video_duration(input_file: str) -> Optional[float]:
    """Get the duration of a video file in seconds."""
    try:
        probe = ffmpeg.probe(input_file)
        duration = float(probe['streams'][0]['duration'])
        return duration
    except Exception as e:
        print(f"Error getting video duration: {e}")
        return None


def add_title_overlay(video_stream, title: str):
    """Adds a title overlay to the video stream."""
    return video_stream.drawtext(
        text=title,
        x='(w-text_w)/2',
        y='h-th-20',
        fontsize=48,
        fontcolor='white',
        shadowcolor='black',
        shadowx=2,
        shadowy=2,

    )


def extract_and_convert_video(
    input_file: str,
    output_file: Optional[str] = None,
    gif_overlay_path: Optional[str] = None,
    logo_overlay_path: Optional[str] = None,
    duration_seconds: int = 30,
    aspect_ratio: str = "9:16",
    title: str = "Music by LexiTone",
):
    """
    Extract a clip from the middle of a video, convert to a specific aspect ratio, and add overlays.
    
    Args:
        input_file: Path to input video file.
        output_file: Path to output video file (optional).
        gif_overlay_path: Path to GIF file for overlay (optional).
        logo_overlay_path: Path to logo file for overlay (optional).
        duration_seconds: Duration of the extracted clip in seconds.
        aspect_ratio: Target aspect ratio (e.g., "9:16").
        title: Text to display as a title on the video.
    """
    
    if not os.path.exists(input_file):
        print(f"Error: Input file '{input_file}' not found.")
        return False
    
    # Get video duration
    video_duration = get_video_duration(input_file)
    if video_duration is None:
        return False
    
    print(f"Video duration: {video_duration:.2f} seconds")
    
    # Check if video is long enough
    if video_duration < duration_seconds:
        print(f"Error: Video is only {video_duration:.2f} seconds long, need at least {duration_seconds} seconds.")
        return False
    
    # Calculate start time for the middle clip
    start_time = (video_duration - duration_seconds) / 2
    print(f"Extracting {duration_seconds} seconds starting from {start_time:.2f} seconds")
    
    # Set output filename if not provided
    if output_file is None:
        input_path = Path(input_file)
        output_file = input_path.with_name(f"{input_path.stem}_extract{input_path.suffix}")
    
    try:
        # Get original video dimensions
        probe = ffmpeg.probe(input_file)
        video_stream_info = next(s for s in probe['streams'] if s['codec_type'] == 'video')
        original_width = int(video_stream_info['width'])
        original_height = int(video_stream_info['height'])
        
        print(f"Original dimensions: {original_width}x{original_height}")
        
        # Calculate target dimensions for the given aspect ratio
        try:
            ratio_width, ratio_height = map(int, aspect_ratio.split(':'))
        except ValueError:
            print(f"Error: Invalid aspect ratio format '{aspect_ratio}'. Please use 'width:height' format (e.g., '9:16').")
            return False

        target_height = original_height
        target_width = int(target_height * ratio_width / ratio_height)
        
        if target_width > original_width:
            target_width = original_width
            target_height = int(target_width * ratio_height / ratio_width)
        
        print(f"Target dimensions for {aspect_ratio} aspect ratio: {target_width}x{target_height}")
        
        # Create ffmpeg stream
        input_stream = ffmpeg.input(input_file, ss=start_time, t=duration_seconds)
        
        # Apply video filters: scale and crop
        video = input_stream.video.filter('scale', target_width, target_height, force_original_aspect_ratio='increase')
        video = video.filter('crop', target_width, target_height)
        
        # Add GIF overlay if provided
        if gif_overlay_path and os.path.exists(gif_overlay_path):
            print(f"Adding GIF overlay from: {gif_overlay_path}")
            gif_stream = ffmpeg.input(gif_overlay_path, stream_loop=-1)
            gif_stream = gif_stream.filter('scale', 'iw*2', 'ih*2')
            video = ffmpeg.overlay(video, gif_stream, x='(W-w)/2', y='(H-h)/2', eof_action='pass')
        elif gif_overlay_path:
            print(f"Warning: GIF overlay file not found at '{gif_overlay_path}'. Skipping overlay.")

        # Add logo overlay if provided
        if logo_overlay_path and os.path.exists(logo_overlay_path):
            print(f"Adding logo overlay from: {logo_overlay_path}")
            logo_stream = ffmpeg.input(logo_overlay_path)
            logo_stream = logo_stream.filter('scale', 'iw*0.9', 'ih*0.9')
            video = ffmpeg.overlay(video, logo_stream, x='(W-w)/2', y='20')
        elif logo_overlay_path:
            print(f"Warning: Logo overlay file not found at '{logo_overlay_path}'. Skipping overlay.")

        # Add title overlay
        if title:
            video = add_title_overlay(video, title)

        # Get audio stream
        audio = input_stream.audio
        
        # Output with video and audio
        output = ffmpeg.output(video, audio, str(output_file),
                             vcodec='libx264', 
                             acodec='aac',
                             preset='fast',
                             crf=23)
        
        # Run the conversion
        print(f"Processing video... Output will be saved as: {output_file}")
        ffmpeg.run(output, overwrite_output=True, quiet=False)
        
        print(f"Successfully created: {output_file}")
        return True
        
    except Exception as e:
        print(f"Error processing video: {e}")
        return False


def main():
    """Main function to handle command line arguments and run the conversion."""
    
    parser = argparse.ArgumentParser(
        description="Extract a clip from a video, convert its aspect ratio, and optionally add overlays.",
        formatter_class=argparse.RawTextHelpFormatter
    )
    parser.add_argument("input_file", nargs='?', default="video.mp4", 
                        help="Path to the input video file (default: video.mp4)")
    parser.add_argument("output_file", nargs='?', default=None, 
                        help="Path to the output video file (optional, defaults to [input_name]_extract.mp4)")
    parser.add_argument("--gif", default="animated-circle.gif", 
                        help="Path to the GIF file to overlay (default: animated-circle.gif)")
    parser.add_argument("--logo", default="logo.png", 
                        help="Path to the logo file to overlay (default: logo.png)")
    parser.add_argument("--duration", type=int, default=30, 
                        help="Duration of the extracted clip in seconds (default: 30)")
    parser.add_argument("--aspect_ratio", default="9:16", 
                        help="Target aspect ratio, e.g., '9:16' (default: 9:16)")
    parser.add_argument("--title", default="Music by LexiTone",
                        help="Title to display on the video (default: 'Music by LexiTone')")
    args = parser.parse_args()

    print(f"Processing: {args.input_file}")
    
    success = extract_and_convert_video(
        args.input_file,
        args.output_file,
        gif_overlay_path=args.gif,
        logo_overlay_path=args.logo,
        duration_seconds=args.duration,
        aspect_ratio=args.aspect_ratio,
        title=args.title,
    )
    
    if success:
        print("Video processing completed successfully!")
    else:
        print("Video processing failed.")
        sys.exit(1)


if __name__ == "__main__":
    main()
